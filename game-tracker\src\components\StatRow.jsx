const StatRow = ({ stats }) => {
  const formatNumber = (num) => {
    return new Intl.NumberFormat('zh-TW').format(num || 0);
  };

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    return `${minutes}分鐘`;
  };

  if (!stats) return null;

  const {
    potionCost,
    moneyChange,
    originalMoneyChange,
    experienceGain,
    experiencePerMinute,
    accumulatedTime
  } = stats;

  // 計算淨收益（金錢變化 - 藥水花費）
  const netProfit = originalMoneyChange - potionCost;

  return (
    <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 mx-4 my-2 border-l-4 border-blue-400">
      <div className="text-center text-sm font-medium text-gray-600 mb-3">
        📊 統計結果 (累計時間: {formatTime(accumulatedTime)})
      </div>
      
      <div className="grid grid-cols-5 gap-4 text-sm">
        {/* 藥水花費 */}
        <div className="bg-red-50 p-3 rounded-lg text-center">
          <div className="font-medium text-red-700 mb-1">藥水花費</div>
          <div className="text-red-600 font-bold">{formatNumber(potionCost)} 塊</div>
        </div>

        {/* 金錢變化（淨收益） */}
        <div className={`p-3 rounded-lg text-center ${
          netProfit >= 0 
            ? 'bg-green-100 border border-green-200' 
            : 'bg-red-100 border border-red-200'
        }`}>
          <div className={`font-medium mb-1 ${
            netProfit >= 0 ? 'text-green-700' : 'text-red-700'
          }`}>
            金錢變化
          </div>
          <div className={`font-bold ${
            netProfit >= 0 ? 'text-green-600' : 'text-red-600'
          }`}>
            {netProfit >= 0 ? '+' : ''}{formatNumber(netProfit)} 塊
          </div>
        </div>

        {/* 獲得金錢 */}
        <div className="bg-blue-50 p-3 rounded-lg text-center">
          <div className="font-medium text-blue-700 mb-1">獲得金錢</div>
          <div className={`font-bold ${
            originalMoneyChange >= 0 ? 'text-blue-600' : 'text-red-600'
          }`}>
            {originalMoneyChange >= 0 ? '+' : ''}{formatNumber(originalMoneyChange)} 塊
          </div>
        </div>

        {/* 經驗增加 */}
        <div className="bg-purple-50 p-3 rounded-lg text-center">
          <div className="font-medium text-purple-700 mb-1">經驗增加</div>
          <div className="text-purple-600 font-bold">+{formatNumber(experienceGain)}</div>
        </div>

        {/* 每分鐘經驗 */}
        <div className="bg-yellow-50 p-3 rounded-lg text-center">
          <div className="font-medium text-yellow-700 mb-1">每分鐘經驗</div>
          <div className="text-yellow-600 font-bold">
            {experiencePerMinute ? formatNumber(experiencePerMinute) : '0'}/分
          </div>
        </div>
      </div>
    </div>
  );
};

export default StatRow;
