import { useState, useEffect, useRef } from 'react';

const Timer = ({ onTimerComplete }) => {
  const [timeLeft, setTimeLeft] = useState(0);
  const [isRunning, setIsRunning] = useState(false);
  const [isCompleted, setIsCompleted] = useState(false);
  const intervalRef = useRef(null);

  useEffect(() => {
    if (isRunning && timeLeft > 0) {
      intervalRef.current = setInterval(() => {
        setTimeLeft(prev => {
          if (prev <= 1) {
            setIsRunning(false);
            setIsCompleted(true);
            if (onTimerComplete) {
              onTimerComplete();
            }
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    } else {
      clearInterval(intervalRef.current);
    }

    return () => clearInterval(intervalRef.current);
  }, [isRunning, timeLeft, onTimerComplete]);

  const startTimer = () => {
    setTimeLeft(600); // 10分鐘 = 600秒
    setIsRunning(true);
    setIsCompleted(false);
  };

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  if (isRunning || timeLeft > 0) {
    return (
      <div className="text-center">
        <div className="text-2xl font-mono font-bold text-blue-600 mb-2">
          {formatTime(timeLeft)}
        </div>
        <div className="text-sm text-gray-500">倒數中...</div>
      </div>
    );
  }

  return (
    <button
      onClick={startTimer}
      disabled={isCompleted}
      className={`px-4 py-2 rounded-md font-medium ${
        isCompleted
          ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
          : 'bg-green-500 text-white hover:bg-green-600'
      }`}
    >
      {isCompleted ? '已完成' : '開始'}
    </button>
  );
};

export default Timer;
