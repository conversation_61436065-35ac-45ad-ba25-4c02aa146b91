import { useState, useEffect, useRef } from 'react';

const Timer = ({ accumulatedTime = 0, onTimeUpdate }) => {
  const [currentTime, setCurrentTime] = useState(accumulatedTime);
  const [isRunning, setIsRunning] = useState(false);
  const [isCompleted, setIsCompleted] = useState(false);
  const intervalRef = useRef(null);

  useEffect(() => {
    if (isRunning) {
      intervalRef.current = setInterval(() => {
        setCurrentTime(prev => {
          const newTime = prev + 1;
          if (onTimeUpdate) {
            onTimeUpdate(newTime);
          }
          return newTime;
        });
      }, 1000);
    } else {
      clearInterval(intervalRef.current);
    }

    return () => clearInterval(intervalRef.current);
  }, [isRunning, onTimeUpdate]);

  const handleStart = () => {
    setIsRunning(true);
  };

  const handlePause = () => {
    setIsRunning(false);
    setIsCompleted(true);
  };

  const formatTime = (seconds) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  if (isRunning) {
    return (
      <div className="text-center">
        <div className="text-lg font-mono font-bold text-blue-600 mb-2">
          {formatTime(currentTime)}
        </div>
        <button
          onClick={handlePause}
          className="px-3 py-1 bg-orange-500 text-white rounded-md hover:bg-orange-600 text-sm"
        >
          暫停
        </button>
      </div>
    );
  }

  if (isCompleted) {
    return (
      <div className="text-center">
        <div className="text-lg font-mono font-bold text-gray-600 mb-2">
          {formatTime(currentTime)}
        </div>
        <button
          disabled
          className="px-3 py-1 bg-gray-300 text-gray-500 rounded-md cursor-not-allowed text-sm"
        >
          已完成
        </button>
      </div>
    );
  }

  return (
    <div className="text-center">
      <div className="text-lg font-mono font-bold text-gray-600 mb-2">
        {formatTime(currentTime)}
      </div>
      <button
        onClick={handleStart}
        className="px-3 py-1 bg-green-500 text-white rounded-md hover:bg-green-600 text-sm"
      >
        開始
      </button>
    </div>
  );
};

export default Timer;
