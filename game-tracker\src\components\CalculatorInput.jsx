import { useState } from 'react';

const CalculatorInput = ({ value, onChange, placeholder, className = "" }) => {
  const [inputValue, setInputValue] = useState(value?.toString() || '');
  const [isEditing, setIsEditing] = useState(false);

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      calculateResult();
    }
  };

  const calculateResult = () => {
    try {
      // 簡單的數學表達式計算
      // 只允許數字、+、-、*、/、()、空格
      const sanitized = inputValue.replace(/[^0-9+\-*/().\s]/g, '');
      if (sanitized !== inputValue) {
        setInputValue(sanitized);
        return;
      }
      
      // 使用 Function 構造函數安全地計算表達式
      const result = Function('"use strict"; return (' + sanitized + ')')();
      
      if (isNaN(result) || !isFinite(result)) {
        throw new Error('Invalid calculation');
      }
      
      const finalValue = Math.round(result);
      setInputValue(finalValue.toString());
      onChange(finalValue);
      setIsEditing(false);
    } catch (error) {
      // 如果計算失敗，保持原值
      setInputValue(value?.toString() || '');
      setIsEditing(false);
    }
  };

  const handleFocus = () => {
    setIsEditing(true);
  };

  const handleBlur = () => {
    if (isEditing) {
      calculateResult();
    }
  };

  const handleChange = (e) => {
    setInputValue(e.target.value);
  };

  return (
    <input
      type="text"
      value={inputValue}
      onChange={handleChange}
      onKeyPress={handleKeyPress}
      onFocus={handleFocus}
      onBlur={handleBlur}
      placeholder={placeholder}
      className={`px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${className}`}
    />
  );
};

export default CalculatorInput;
