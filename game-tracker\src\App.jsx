import { useState } from 'react'
import GameRow from './components/GameRow'

function App() {
  const [gameRows, setGameRows] = useState([
    {
      id: 1,
      experience: 0,
      redPotion: 0,
      bluePotion: 0,
      whitePotion: 0,
      money: 0
    }
  ]);

  const addNewRow = () => {
    const newId = Math.max(...gameRows.map(row => row.id)) + 1;
    setGameRows([...gameRows, {
      id: newId,
      experience: 0,
      redPotion: 0,
      bluePotion: 0,
      whitePotion: 0,
      money: 0
    }]);
  };

  const updateRow = (index, newData) => {
    const updatedRows = [...gameRows];
    updatedRows[index] = newData;
    setGameRows(updatedRows);
  };

  const calculateStats = (currentIndex) => {
    if (currentIndex === 0) return null;

    const current = gameRows[currentIndex];
    const previous = gameRows[currentIndex - 1];

    const potionCost =
      (current.redPotion - previous.redPotion) * 47 +
      (current.bluePotion - previous.bluePotion) * 190 +
      (current.whitePotion - previous.whitePotion) * 304;

    const experienceGain = current.experience - previous.experience;
    const moneyChange = current.money - previous.money;

    return {
      potionCost,
      experienceGain,
      moneyChange
    };
  };

  return (
    <div className="min-h-screen bg-gray-100 py-8">
      <div className="max-w-7xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-gray-900 mb-8 text-center">
          遊戲追蹤器
        </h1>

        <div className="space-y-4">
          {gameRows.map((row, index) => (
            <GameRow
              key={row.id}
              data={row}
              onChange={(newData) => updateRow(index, newData)}
              onAddRow={addNewRow}
              showStats={index > 0}
              stats={calculateStats(index)}
              isFirst={index === 0}
            />
          ))}
        </div>
      </div>
    </div>
  )
}

export default App
