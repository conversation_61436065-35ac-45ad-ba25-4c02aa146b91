import CalculatorInput from './CalculatorInput';
import Timer from './Timer';

const GameRow = ({
  data,
  onChange,
  onAddRow
}) => {
  const handleChange = (field, value) => {
    onChange({ ...data, [field]: value });
  };

  const formatNumber = (num) => {
    return new Intl.NumberFormat('zh-TW').format(num || 0);
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-4 mb-4">
      <div className="grid grid-cols-7 gap-4 items-center">
        {/* 經驗值 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            經驗值
          </label>
          <CalculatorInput
            value={data.experience}
            onChange={(value) => handleChange('experience', value)}
            placeholder="輸入經驗值"
            className="w-full"
          />
        </div>

        {/* 紅色藥水 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            紅色藥水 (47塊)
          </label>
          <CalculatorInput
            value={data.redPotion}
            onChange={(value) => handleChange('redPotion', value)}
            placeholder="0"
            className="w-full"
          />
        </div>

        {/* 藍色藥水 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            藍色藥水 (190塊)
          </label>
          <CalculatorInput
            value={data.bluePotion}
            onChange={(value) => handleChange('bluePotion', value)}
            placeholder="0"
            className="w-full"
          />
        </div>

        {/* 白色藥水 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            白色藥水 (304塊)
          </label>
          <CalculatorInput
            value={data.whitePotion}
            onChange={(value) => handleChange('whitePotion', value)}
            placeholder="0"
            className="w-full"
          />
        </div>

        {/* 金錢 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            金錢
          </label>
          <CalculatorInput
            value={data.money}
            onChange={(value) => handleChange('money', value)}
            placeholder="0"
            className="w-full"
          />
          <div className="text-xs text-gray-500 mt-1">
            {formatNumber(data.money)}
          </div>
        </div>

        {/* 計時器 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            累計時間
          </label>
          <Timer
            accumulatedTime={data.accumulatedTime || 0}
            onTimeUpdate={(time) => handleChange('accumulatedTime', time)}
          />
        </div>

        {/* 添加按鈕 */}
        <div className="flex justify-center">
          <button
            onClick={onAddRow}
            className="w-10 h-10 bg-blue-500 text-white rounded-full hover:bg-blue-600 flex items-center justify-center text-xl font-bold"
            title="添加新行"
          >
            +
          </button>
        </div>
      </div>


    </div>
  );
};

export default GameRow;
