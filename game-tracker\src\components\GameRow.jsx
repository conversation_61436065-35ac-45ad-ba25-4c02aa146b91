import CalculatorInput from './CalculatorInput';
import Timer from './Timer';

const GameRow = ({ 
  data, 
  onChange, 
  onAddRow, 
  showStats = false, 
  stats = null,
  isFirst = false 
}) => {
  const handleChange = (field, value) => {
    onChange({ ...data, [field]: value });
  };

  const formatNumber = (num) => {
    return new Intl.NumberFormat('zh-TW').format(num || 0);
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-4 mb-4">
      <div className="grid grid-cols-7 gap-4 items-center">
        {/* 經驗值 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            經驗值
          </label>
          <CalculatorInput
            value={data.experience}
            onChange={(value) => handleChange('experience', value)}
            placeholder="輸入經驗值"
            className="w-full"
          />
        </div>

        {/* 紅色藥水 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            紅色藥水 (47塊)
          </label>
          <CalculatorInput
            value={data.redPotion}
            onChange={(value) => handleChange('redPotion', value)}
            placeholder="0"
            className="w-full"
          />
        </div>

        {/* 藍色藥水 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            藍色藥水 (190塊)
          </label>
          <CalculatorInput
            value={data.bluePotion}
            onChange={(value) => handleChange('bluePotion', value)}
            placeholder="0"
            className="w-full"
          />
        </div>

        {/* 白色藥水 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            白色藥水 (304塊)
          </label>
          <CalculatorInput
            value={data.whitePotion}
            onChange={(value) => handleChange('whitePotion', value)}
            placeholder="0"
            className="w-full"
          />
        </div>

        {/* 金錢 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            金錢
          </label>
          <CalculatorInput
            value={data.money}
            onChange={(value) => handleChange('money', value)}
            placeholder="0"
            className="w-full"
          />
          <div className="text-xs text-gray-500 mt-1">
            {formatNumber(data.money)}
          </div>
        </div>

        {/* 計時器 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            計時器
          </label>
          <Timer onTimerComplete={() => console.log('Timer completed')} />
        </div>

        {/* 添加按鈕 */}
        <div className="flex justify-center">
          <button
            onClick={onAddRow}
            className="w-10 h-10 bg-blue-500 text-white rounded-full hover:bg-blue-600 flex items-center justify-center text-xl font-bold"
            title="添加新行"
          >
            +
          </button>
        </div>
      </div>

      {/* 統計信息 */}
      {showStats && stats && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="grid grid-cols-3 gap-4 text-sm">
            <div className="bg-red-50 p-3 rounded">
              <div className="font-medium text-red-700">藥水花費</div>
              <div className="text-red-600">{formatNumber(stats.potionCost)} 塊</div>
            </div>
            <div className="bg-green-50 p-3 rounded">
              <div className="font-medium text-green-700">經驗增加</div>
              <div className="text-green-600">+{formatNumber(stats.experienceGain)}</div>
            </div>
            <div className="bg-blue-50 p-3 rounded">
              <div className="font-medium text-blue-700">金錢變化</div>
              <div className={`${stats.moneyChange >= 0 ? 'text-blue-600' : 'text-red-600'}`}>
                {stats.moneyChange >= 0 ? '+' : ''}{formatNumber(stats.moneyChange)}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default GameRow;
